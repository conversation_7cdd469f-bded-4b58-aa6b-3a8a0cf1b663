{"buildFiles": ["/Users/<USER>/FlutterDev/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Downloads/projects/usahs_guard/android/app/.cxx/RelWithDebInfo/496w1s6h/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Downloads/projects/usahs_guard/android/app/.cxx/RelWithDebInfo/496w1s6h/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}