name: guardtracker
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.4.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  get: ^4.6.1
  shimmer: ^3.0.0
  shared_preferences: ^2.3.5
  flutter_screenutil: ^5.7.0
  flutter_easyloading: ^3.0.3

  geocoding: ^3.0.0

  flutter_foreground_task: ^6.0.0
  fl_location: ^5.0.0




  permission_handler: ^12.0.0+1

  flutter_launcher_icons: ^0.13.1
  photo_view: ^0.15.0
  animated_bottom_navigation_bar: ^1.3.3
  avatar_glow: ^3.0.1
  dio: ^5.4.3+1
  intl: ^0.19.0
  image_picker: ^0.8.7
  path_provider: ^2.1.5
  signature: ^5.0.0
  location: ^8.0.0
  android_intent_plus: ^5.3.0
dev_dependencies:
  flutter_test:
    sdk: flutter


  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
flutter_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.jpeg"
  remove_alpha_ios: true
# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/
    - assets/icons/
    - assets/images/
  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Inter_18pt-ExtraBold.ttf

    - family: RobotoRegular
      fonts:
        - asset: assets/fonts/Inter_28pt-Regular.ttf
    - family: RobotoMedium
      fonts:
        - asset: assets/fonts/Inter_28pt-Regular.ttf
    - family: RobotoLight
      fonts:
        - asset: assets/fonts/Inter_28pt-Light.ttf
