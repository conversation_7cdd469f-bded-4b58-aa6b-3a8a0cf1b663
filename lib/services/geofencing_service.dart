import 'dart:async';
import 'dart:math';
import 'package:guardtracker/services/location_service.dart';

/// Geofence data model
class Geofence {
  final String id;
  final double latitude;
  final double longitude;
  final double radius; // in meters
  final String name;
  final bool isActive;

  Geofence({
    required this.id,
    required this.latitude,
    required this.longitude,
    required this.radius,
    required this.name,
    this.isActive = true,
  });

  /// Calculate distance from a point to this geofence center
  double distanceFrom(double lat, double lng) {
    return _calculateDistance(latitude, longitude, lat, lng);
  }

  /// Check if a point is inside this geofence
  bool contains(double lat, double lng) {
    return distanceFrom(lat, lng) <= radius;
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'latitude': latitude,
        'longitude': longitude,
        'radius': radius,
        'name': name,
        'isActive': isActive,
      };

  factory Geofence.fromJson(Map<String, dynamic> json) => Geofence(
        id: json['id'],
        latitude: json['latitude'],
        longitude: json['longitude'],
        radius: json['radius'],
        name: json['name'],
        isActive: json['isActive'] ?? true,
      );
}

/// Geofence event types
enum GeofenceEventType { enter, exit, dwell }

/// Geofence event data
class GeofenceEvent {
  final Geofence geofence;
  final GeofenceEventType eventType;
  final LocationData location;
  final DateTime timestamp;

  GeofenceEvent({
    required this.geofence,
    required this.eventType,
    required this.location,
    required this.timestamp,
  });

  @override
  String toString() =>
      'GeofenceEvent(${eventType.name} ${geofence.name} at ${location.latitude}, ${location.longitude})';
}

/// Geofencing service for monitoring work area boundaries
class GeofencingService {
  static final GeofencingService _instance = GeofencingService._();
  static GeofencingService get instance => _instance;
  
  GeofencingService._();

  final List<Geofence> _geofences = [];
  final Map<String, bool> _currentGeofenceStates = {}; // geofenceId -> isInside
  final Map<String, DateTime> _lastEntryTimes = {}; // geofenceId -> entry time
  
  final StreamController<GeofenceEvent> _eventController =
      StreamController<GeofenceEvent>.broadcast();

  /// Stream of geofence events
  Stream<GeofenceEvent> get eventStream => _eventController.stream;

  /// Add a geofence to monitor
  void addGeofence(Geofence geofence) {
    _geofences.removeWhere((g) => g.id == geofence.id);
    _geofences.add(geofence);
    _currentGeofenceStates[geofence.id] = false;
    print('Added geofence: ${geofence.name} (${geofence.radius}m radius)');
  }

  /// Remove a geofence
  void removeGeofence(String geofenceId) {
    _geofences.removeWhere((g) => g.id == geofenceId);
    _currentGeofenceStates.remove(geofenceId);
    _lastEntryTimes.remove(geofenceId);
    print('Removed geofence: $geofenceId');
  }

  /// Clear all geofences
  void clearGeofences() {
    _geofences.clear();
    _currentGeofenceStates.clear();
    _lastEntryTimes.clear();
    print('Cleared all geofences');
  }

  /// Process a location update and check for geofence events
  void processLocationUpdate(LocationData location) {
    for (final geofence in _geofences) {
      if (!geofence.isActive) continue;

      final isCurrentlyInside = geofence.contains(location.latitude, location.longitude);
      final wasInside = _currentGeofenceStates[geofence.id] ?? false;

      if (isCurrentlyInside && !wasInside) {
        // Entered geofence
        _currentGeofenceStates[geofence.id] = true;
        _lastEntryTimes[geofence.id] = location.timestamp;
        
        final event = GeofenceEvent(
          geofence: geofence,
          eventType: GeofenceEventType.enter,
          location: location,
          timestamp: location.timestamp,
        );
        
        _eventController.add(event);
        print('Geofence ENTER: ${geofence.name}');
        
      } else if (!isCurrentlyInside && wasInside) {
        // Exited geofence
        _currentGeofenceStates[geofence.id] = false;
        _lastEntryTimes.remove(geofence.id);
        
        final event = GeofenceEvent(
          geofence: geofence,
          eventType: GeofenceEventType.exit,
          location: location,
          timestamp: location.timestamp,
        );
        
        _eventController.add(event);
        print('Geofence EXIT: ${geofence.name}');
      }
    }
  }

  /// Check if currently inside any geofence
  bool isInsideAnyGeofence() {
    return _currentGeofenceStates.values.any((isInside) => isInside);
  }

  /// Check if currently inside a specific geofence
  bool isInsideGeofence(String geofenceId) {
    return _currentGeofenceStates[geofenceId] ?? false;
  }

  /// Get all active geofences
  List<Geofence> get activeGeofences => 
      _geofences.where((g) => g.isActive).toList();

  /// Get current geofence states
  Map<String, bool> get currentStates => Map.from(_currentGeofenceStates);

  /// Create a work area geofence from API response
  static Geofence createWorkAreaGeofence({
    required String workAreaId,
    required double latitude,
    required double longitude,
    required double radius,
    required String name,
  }) {
    return Geofence(
      id: 'work_area_$workAreaId',
      latitude: latitude,
      longitude: longitude,
      radius: radius,
      name: name,
    );
  }

  void dispose() {
    _eventController.close();
    _geofences.clear();
    _currentGeofenceStates.clear();
    _lastEntryTimes.clear();
  }
}

/// Calculate distance between two points using Haversine formula
double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
  const double earthRadius = 6371000; // Earth's radius in meters
  
  final double dLat = _degreesToRadians(lat2 - lat1);
  final double dLon = _degreesToRadians(lon2 - lon1);
  
  final double a = sin(dLat / 2) * sin(dLat / 2) +
      cos(_degreesToRadians(lat1)) * cos(_degreesToRadians(lat2)) *
      sin(dLon / 2) * sin(dLon / 2);
  
  final double c = 2 * atan2(sqrt(a), sqrt(1 - a));
  
  return earthRadius * c;
}

double _degreesToRadians(double degrees) {
  return degrees * (pi / 180);
}
