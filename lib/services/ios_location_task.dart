import 'dart:async';
import 'dart:io';
import 'dart:isolate';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:location/location.dart' as loc;
import 'package:guardtracker/core/constants/api_endpoints.dart';
import 'package:guardtracker/core/services/http_service.dart';
import 'package:guardtracker/services/location_service.dart';

/// iOS-specific foreground task for continuous location tracking
class IOSLocationTaskHandler extends TaskHandler {
  static const String taskName = 'IOSLocationTask';
  static const int locationUpdateInterval = 15; // seconds
  
  final loc.Location _location = loc.Location();
  Timer? _locationTimer;
  SendPort? _sendPort;
  int _updateCount = 0;

  @override
  Future<void> onStart(DateTime timestamp, SendPort? sendPort) async {
    _sendPort = sendPort;
    
    print('iOS Location Task started at $timestamp');
    
    // Configure location settings for background
    await _configureLocationForBackground();
    
    // Start periodic location updates
    _startPeriodicLocationUpdates();
    
    // Send initial status
    _sendPort?.send({
      'type': 'status',
      'message': 'iOS location task started',
      'timestamp': timestamp.toIso8601String(),
    });
  }

  @override
  Future<void> onRepeatEvent(DateTime timestamp, SendPort? sendPort) async {
    // This is called periodically by the foreground task
    await _performLocationUpdate();
  }

  @override
  Future<void> onDestroy(DateTime timestamp, SendPort? sendPort) async {
    print('iOS Location Task destroyed at $timestamp');
    
    _locationTimer?.cancel();
    _locationTimer = null;
    
    _sendPort?.send({
      'type': 'status',
      'message': 'iOS location task stopped',
      'timestamp': timestamp.toIso8601String(),
    });
  }

  @override
  void onButtonPressed(String id) {
    switch (id) {
      case 'force_update':
        _performLocationUpdate();
        break;
      case 'stop_tracking':
        FlutterForegroundTask.stopService();
        break;
    }
  }

  /// Configure location settings optimized for iOS background tracking
  Future<void> _configureLocationForBackground() async {
    try {
      await _location.changeSettings(
        accuracy: loc.LocationAccuracy.high,
        interval: locationUpdateInterval * 1000, // Convert to milliseconds
        distanceFilter: 5.0, // 5 meters minimum movement
      );
      
      // Enable background mode
      await _location.enableBackgroundMode(enable: true);
      
      print('iOS location configured for background tracking');
    } catch (e) {
      print('Failed to configure iOS location for background: $e');
    }
  }

  /// Start periodic location updates
  void _startPeriodicLocationUpdates() {
    _locationTimer?.cancel();
    _locationTimer = Timer.periodic(
      Duration(seconds: locationUpdateInterval),
      (_) => _performLocationUpdate(),
    );
  }

  /// Perform a single location update
  Future<void> _performLocationUpdate() async {
    try {
      final locationData = await _location.getLocation()
          .timeout(const Duration(seconds: 30));

      if (locationData.latitude == null || locationData.longitude == null) {
        print('iOS Task: Invalid location data received');
        return;
      }

      _updateCount++;
      
      final location = LocationData.fromLocationData(locationData);
      
      print('iOS Task: Location update #$_updateCount: $location (accuracy: ${location.accuracy?.toStringAsFixed(1)}m)');

      // Upload location to server
      await _uploadLocation(location);

      // Send status to main app
      _sendPort?.send({
        'type': 'location_update',
        'latitude': location.latitude,
        'longitude': location.longitude,
        'accuracy': location.accuracy,
        'timestamp': location.timestamp.toIso8601String(),
        'update_count': _updateCount,
      });

    } catch (e) {
      print('iOS Task: Location update failed: $e');
      
      _sendPort?.send({
        'type': 'error',
        'message': 'Location update failed: $e',
        'timestamp': DateTime.now().toIso8601String(),
      });
    }
  }

  /// Upload location to server
  Future<void> _uploadLocation(LocationData location) async {
    try {
      await ApiService.postData(
        Endpoints.updateLocation,
        location.toJson(),
      ).timeout(const Duration(seconds: 15));
      
      print('iOS Task: Location uploaded successfully');
    } catch (e) {
      print('iOS Task: Location upload failed: $e');
      // Don't throw error, just log it
    }
  }
}

/// iOS Location Task Manager
class IOSLocationTaskManager {
  static final IOSLocationTaskManager _instance = IOSLocationTaskManager._();
  static IOSLocationTaskManager get instance => _instance;
  
  IOSLocationTaskManager._();
  
  bool _isInitialized = false;
  bool _isRunning = false;
  ReceivePort? _receivePort;
  StreamController<Map<String, dynamic>>? _statusController;

  /// Initialize the iOS location task
  Future<bool> initialize() async {
    if (_isInitialized) return true;
    
    try {
      // Initialize foreground task
      FlutterForegroundTask.init(
        androidNotificationOptions: AndroidNotificationOptions(
          channelId: 'ios_location_task',
          channelName: 'iOS Location Tracking',
          channelDescription: 'Continuous location tracking for iOS',
          channelImportance: NotificationChannelImportance.LOW,
          priority: NotificationPriority.LOW,
          iconData: const NotificationIconData(
            resType: ResourceType.mipmap,
            resPrefix: ResourcePrefix.ic,
            name: 'launcher',
          ),
        ),
        iosNotificationOptions: const IOSNotificationOptions(
          showNotification: true,
          playSound: false,
        ),
        foregroundTaskOptions: const ForegroundTaskOptions(
          interval: 15000, // 15 seconds
          isOnceEvent: false,
          autoRunOnBoot: true,
          allowWakeLock: true,
          allowWifiLock: true,
        ),
      );

      _isInitialized = true;
      print('iOS Location Task Manager initialized');
      return true;
    } catch (e) {
      print('Failed to initialize iOS Location Task Manager: $e');
      return false;
    }
  }

  /// Start the iOS location task
  Future<bool> startTask() async {
    if (!_isInitialized) {
      final initialized = await initialize();
      if (!initialized) return false;
    }

    if (_isRunning) {
      print('iOS location task is already running');
      return true;
    }

    try {
      // Setup receive port for communication
      _receivePort = ReceivePort();
      _statusController = StreamController<Map<String, dynamic>>.broadcast();
      
      _receivePort!.listen((data) {
        if (data is Map<String, dynamic>) {
          _statusController?.add(data);
          print('iOS Task Status: ${data['type']} - ${data['message']}');
        }
      });

      // Start the foreground service
      final serviceRequestResult = await FlutterForegroundTask.startService(
        notificationTitle: 'Guard Location Tracking',
        notificationText: 'Tracking your location for attendance',
        notificationButtons: [
          const NotificationButton(
            id: 'force_update',
            text: 'Update Now',
          ),
          const NotificationButton(
            id: 'stop_tracking',
            text: 'Stop',
          ),
        ],
        callback: startIOSLocationTask,
      );

      if (serviceRequestResult is ServiceRequestSuccess) {
        _isRunning = true;
        print('iOS location task started successfully');
        return true;
      } else {
        print('Failed to start iOS location task: $serviceRequestResult');
        return false;
      }
    } catch (e) {
      print('Error starting iOS location task: $e');
      return false;
    }
  }

  /// Stop the iOS location task
  Future<void> stopTask() async {
    if (!_isRunning) return;

    try {
      await FlutterForegroundTask.stopService();
      
      _receivePort?.close();
      _receivePort = null;
      
      await _statusController?.close();
      _statusController = null;
      
      _isRunning = false;
      print('iOS location task stopped');
    } catch (e) {
      print('Error stopping iOS location task: $e');
    }
  }

  /// Get status stream
  Stream<Map<String, dynamic>>? get statusStream => _statusController?.stream;

  /// Check if task is running
  bool get isRunning => _isRunning;
}

/// Entry point for the iOS location task
@pragma('vm:entry-point')
void startIOSLocationTask() {
  FlutterForegroundTask.setTaskHandler(IOSLocationTaskHandler());
}
