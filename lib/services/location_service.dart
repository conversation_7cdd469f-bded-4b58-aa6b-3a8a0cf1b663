import 'dart:async';
import 'dart:io';
import 'package:android_intent_plus/android_intent.dart';
import 'package:guardtracker/core/constants/api_endpoints.dart';
import 'package:guardtracker/core/services/http_service.dart';
import 'package:location/location.dart' as loc;
import 'package:permission_handler/permission_handler.dart';

/// Configuration for location tracking
class LocationConfig {
  // iOS optimized intervals
  static const int backgroundIntervalMs = 15000; // 15 seconds for iOS
  static const int foregroundIntervalMs = 10000; // 10 seconds for active tracking
  static const int iosSignificantLocationIntervalMs = 5000; // 5 seconds for significant changes

  // Distance filters
  static const double distanceFilter = 5.0; // 5 meters minimum movement
  static const double iosSignificantDistanceFilter = 10.0; // 10 meters for significant changes

  // Upload configuration
  static const int uploadBatchSize = 1;
  static const int maxRetryAttempts = 3;
  static const int maxPendingLocations = 5; // Increased for better buffering

  // Timing configuration
  static const Duration retryDelay = Duration(seconds: 5);
  static const Duration serverTimeout = Duration(seconds: 15);
  static const Duration uploadInterval = Duration(seconds: 20); // More frequent uploads

  // iOS specific configuration
  static const Duration iosLocationTimeout = Duration(seconds: 30);
  static const Duration iosBackgroundRefreshInterval = Duration(minutes: 1);
}

/// Enhanced location data model
class LocationData {
  final double latitude;
  final double longitude;
  final DateTime timestamp;
  final double? accuracy;
  final double? altitude;
  final double? speed;
  final double? heading;

  LocationData({
    required this.latitude,
    required this.longitude,
    required this.timestamp,
    this.accuracy,
    this.altitude,
    this.speed,
    this.heading,
  });

  Map<String, dynamic> toJson() => {
        'latitude': latitude,
        'longitude': longitude,
        'timestamp': timestamp.toIso8601String(),
        'accuracy': accuracy,
        'altitude': altitude,
        'speed': speed,
        'heading': heading,
      };

  factory LocationData.fromLocationData(loc.LocationData locationData) =>
      LocationData(
        latitude: locationData.latitude!,
        longitude: locationData.longitude!,
        timestamp: DateTime.now(),
        accuracy: locationData.accuracy,
        altitude: locationData.altitude,
        speed: locationData.speed,
        heading: locationData.heading,
      );

  bool isValid() {
    return latitude.abs() <= 90 &&
        longitude.abs() <= 180 &&
        (accuracy == null || accuracy! < 1000);
  }

  @override
  String toString() =>
      'LocationData(${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)})';
}

/// Handles uploading locations with batching and retry logic
class LocationUploadManager {
  static final LocationUploadManager _instance = LocationUploadManager._();
  static LocationUploadManager get instance => _instance;

  final List<LocationData> _pendingLocations = [];
  bool _isUploading = false;
  Timer? _uploadTimer;
  DateTime? _lastSuccessfulUpload;

  LocationUploadManager._() {
    _startPeriodicUpload();
  }

  void _startPeriodicUpload() {
    _uploadTimer?.cancel();
    _uploadTimer = Timer.periodic(LocationConfig.uploadInterval, (_) {
      _tryBatchUpload();
    });
  }

  void addLocation(LocationData location) {
    if (!location.isValid()) {
      print('Invalid location data rejected: $location');
      return;
    }

    _pendingLocations.add(location);

    // Maintain max pending locations
    if (_pendingLocations.length > LocationConfig.maxPendingLocations) {
      _pendingLocations.removeAt(0);
      print('Removed oldest location due to queue limit');
    }

    print('Location queued. Pending: ${_pendingLocations.length}');

    // Try immediate upload if conditions are met
    _tryBatchUpload();
  }

  Future<void> _tryBatchUpload() async {
    if (_isUploading || _pendingLocations.isEmpty) return;

    final shouldUpload =
        _pendingLocations.length >= LocationConfig.uploadBatchSize ||
            _shouldUploadByTime();

    if (shouldUpload) {
      await _performBatchUpload();
    }
  }

  bool _shouldUploadByTime() {
    if (_pendingLocations.isEmpty) return false;

    final oldestLocation = _pendingLocations.first;
    return DateTime.now().difference(oldestLocation.timestamp) >
        const Duration(minutes: 1);
  }

  Future<void> _performBatchUpload() async {
    if (_isUploading || _pendingLocations.isEmpty) return;

    _isUploading = true;
    final uploadCount = _pendingLocations.length;

    try {
      print('Starting batch upload of $uploadCount locations');

      final locationsToUpload = List<LocationData>.from(_pendingLocations);
      int successCount = 0;

      for (final location in locationsToUpload) {
        final success = await _uploadSingleLocation(location);

        if (success) {
          _pendingLocations.remove(location);
          successCount++;
          _lastSuccessfulUpload = DateTime.now();
        } else {
          // Stop on first failure to maintain order
          break;
        }
      }

      print('Batch upload completed: $successCount/$uploadCount successful');
    } catch (e) {
      print('Batch upload error: $e');
    } finally {
      _isUploading = false;
    }
  }

  Future<bool> _uploadSingleLocation(LocationData location) async {
    for (int attempt = 1;
        attempt <= LocationConfig.maxRetryAttempts;
        attempt++) {
      try {
        await ApiService.postData(
          Endpoints.updateLocation,
          location.toJson(),
        ).timeout(LocationConfig.serverTimeout);

        if (attempt > 1) {
          print('Location uploaded successfully after $attempt attempts');
        }
        return true;
      } on TimeoutException {
        print(
            'Upload timeout (attempt $attempt/$LocationConfig.maxRetryAttempts)');
      } catch (e) {
        print(
            'Upload failed (attempt $attempt/${LocationConfig.maxRetryAttempts}): $e');
      }

      if (attempt < LocationConfig.maxRetryAttempts) {
        await Future.delayed(LocationConfig.retryDelay * attempt);
      }
    }

    return false;
  }

  Future<void> forceUpload() async {
    await _performBatchUpload();
  }

  void dispose() {
    _uploadTimer?.cancel();
    _pendingLocations.clear();
  }

  // Getters
  int get pendingCount => _pendingLocations.length;
  bool get isUploading => _isUploading;
  DateTime? get lastSuccessfulUpload => _lastSuccessfulUpload;
}

/// Main location service using the location package
class LocationService {
  static final LocationService _instance = LocationService._();
  factory LocationService() => _instance;
  LocationService._();

  final loc.Location _location = loc.Location();
  StreamSubscription<loc.LocationData>? _locationSubscription;
  StreamSubscription<loc.LocationData>? _significantLocationSubscription;
  bool _isTracking = false;
  bool _isBackgroundEnabled = false;
  bool _isSignificantLocationEnabled = false;
  Timer? _iosLocationTimer;
  Timer? _backgroundRefreshTimer;

  final StreamController<LocationServiceStatus> _statusController =
      StreamController<LocationServiceStatus>.broadcast();

  /// Stream for monitoring service status
  Stream<LocationServiceStatus> get statusStream => _statusController.stream;

  /// Initialize and start location tracking
  Future<LocationServiceResult> startLocationTracking() async {
    try {
      if (_isTracking) {
        return LocationServiceResult.success(
            'Location tracking already active');
      }

      // Check and request permissions
      final permissionResult = await _setupPermissions();
      if (!permissionResult.isSuccess) {
        return permissionResult;
      }

      // Configure location settings
      await _configureLocationSettings();

      // Start location stream
      await _startLocationStream();

      _isTracking = true;
      _emitStatus(LocationServiceStatus.active());

      return LocationServiceResult.success(
          'Location tracking started successfully');
    } catch (e) {
      return LocationServiceResult.failure(
          'Failed to start location tracking: $e');
    }
  }

  /// Setup and request all necessary permissions
  Future<LocationServiceResult> _setupPermissions() async {
    try {
      // Check if location service is enabled
      bool serviceEnabled = await _location.serviceEnabled();
      if (!serviceEnabled) {
        serviceEnabled = await _location.requestService();
        if (!serviceEnabled) {
          print('Location services are disabled');
          return LocationServiceResult.failure(
              'Location services are disabled. Please enable them in device settings.');
        }
      }

      // Check location permissions
      loc.PermissionStatus permissionGranted = await _location.hasPermission();
      if (permissionGranted == loc.PermissionStatus.denied) {
        permissionGranted = await _location.requestPermission();
        if (permissionGranted != loc.PermissionStatus.granted) {
          return LocationServiceResult.failure('Location permission denied');
        }
      }
      await checkAndRequestBackgroundLocation();

      if (Platform.isAndroid) {
        final result = await Permission.ignoreBatteryOptimizations.request();
        if (!result.isGranted) {
          await const AndroidIntent(
            action: 'android.settings.IGNORE_BATTERY_OPTIMIZATION_SETTINGS',
          ).launch();
        }
        print('battery optm status ${result.name}');
      }

      // Request background permission (important for background tracking)
      final backgroundPermission =
          await _location.enableBackgroundMode(enable: true);
      if (backgroundPermission) {
        _isBackgroundEnabled = true;
        print('Background location enabled');
      } else {
        print('Background location not enabled - tracking may be limited');
      }

      return LocationServiceResult.success('All permissions granted');
    } catch (e) {
      return LocationServiceResult.failure('Permission setup failed: $e');
    }
  }

  /// Configure location tracking settings
  Future<void> _configureLocationSettings() async {
    if (Platform.isIOS) {
      // iOS specific configuration for better background tracking
      await _location.changeSettings(
        accuracy: loc.LocationAccuracy.high,
        interval: LocationConfig.foregroundIntervalMs,
        distanceFilter: LocationConfig.distanceFilter,
      );

      // Enable significant location changes for iOS background tracking
      _isSignificantLocationEnabled = true;
    } else {
      // Android configuration
      await _location.changeSettings(
        accuracy: loc.LocationAccuracy.high,
        interval: LocationConfig.foregroundIntervalMs,
        distanceFilter: LocationConfig.distanceFilter,
      );
    }

    // Enable background mode for continuous tracking
    if (Platform.isAndroid && _isBackgroundEnabled) {
      await _location.changeNotificationOptions(
        title: 'Location Tracking Active',
        subtitle: 'App is tracking your location',
        description: 'Location data is being collected and uploaded securely',
        onTapBringToFront: true,
      );
    }
  }

  /// Start the location data stream
  Future<void> _startLocationStream() async {
    // Start regular location updates
    _locationSubscription = _location.onLocationChanged.listen(
      _onLocationUpdate,
      onError: _onLocationError,
      cancelOnError: false,
    );

    // iOS specific: Start significant location changes for background tracking
    if (Platform.isIOS && _isSignificantLocationEnabled) {
      await _startSignificantLocationChanges();
      _startIOSBackgroundRefresh();
    }
  }

  /// Start significant location changes for iOS background tracking
  Future<void> _startSignificantLocationChanges() async {
    try {
      // Configure for significant location changes
      await _location.changeSettings(
        accuracy: loc.LocationAccuracy.high,
        interval: LocationConfig.iosSignificantLocationIntervalMs,
        distanceFilter: LocationConfig.iosSignificantDistanceFilter,
      );

      print('Started iOS significant location changes monitoring');
    } catch (e) {
      print('Failed to start significant location changes: $e');
    }
  }

  /// Start iOS background refresh timer
  void _startIOSBackgroundRefresh() {
    _backgroundRefreshTimer?.cancel();
    _backgroundRefreshTimer = Timer.periodic(
      LocationConfig.iosBackgroundRefreshInterval,
      (_) => _refreshLocationInBackground(),
    );
  }

  /// Refresh location in background for iOS
  Future<void> _refreshLocationInBackground() async {
    if (!_isTracking || !Platform.isIOS) return;

    try {
      final location = await _location.getLocation()
          .timeout(LocationConfig.iosLocationTimeout);

      if (location.latitude != null && location.longitude != null) {
        _onLocationUpdate(location);
        print('iOS background location refresh successful');
      }
    } catch (e) {
      print('iOS background location refresh failed: $e');
    }
  }

  /// Handle incoming location updates
  void _onLocationUpdate(loc.LocationData locationData) {
    if (locationData.latitude == null || locationData.longitude == null) {
      print('Received invalid location data');
      return;
    }

    final location = LocationData.fromLocationData(locationData);

    // Enhanced logging for iOS debugging
    if (Platform.isIOS) {
      print('iOS Location update: $location (accuracy: ${location.accuracy?.toStringAsFixed(1)}m, speed: ${location.speed?.toStringAsFixed(1)}m/s)');
    } else {
      print('Location update: $location (accuracy: ${location.accuracy?.toStringAsFixed(1)}m)');
    }

    // Validate location accuracy for iOS
    if (Platform.isIOS && location.accuracy != null && location.accuracy! > 100) {
      print('iOS location accuracy too low (${location.accuracy}m), skipping update');
      return;
    }

    // Add to upload queue
    LocationUploadManager.instance.addLocation(location);

    // Emit status update
    _emitStatus(LocationServiceStatus.active(
      lastLocation: location,
      pendingUploads: LocationUploadManager.instance.pendingCount,
      isUploading: LocationUploadManager.instance.isUploading,
    ));
  }

  /// Handle location stream errors
  void _onLocationError(dynamic error) {
    print('Location stream error: $error');
    _emitStatus(LocationServiceStatus.error('Location stream error: $error'));
  }

  /// Get single location update
  Future<LocationData?> getCurrentLocation() async {
    try {
      final locationData = await _location.getLocation();

      if (locationData.latitude != null && locationData.longitude != null) {
        return LocationData.fromLocationData(locationData);
      }

      return null;
    } catch (e) {
      print('Failed to get current location: $e');
      return null;
    }
  }

  /// Stop location tracking
  Future<void> stopLocationTracking() async {
    try {
      if (!_isTracking) return;

      // Cancel location subscriptions
      await _locationSubscription?.cancel();
      _locationSubscription = null;

      await _significantLocationSubscription?.cancel();
      _significantLocationSubscription = null;

      // Cancel iOS specific timers
      _iosLocationTimer?.cancel();
      _iosLocationTimer = null;

      _backgroundRefreshTimer?.cancel();
      _backgroundRefreshTimer = null;

      // Disable background mode
      if (_isBackgroundEnabled) {
        await _location.enableBackgroundMode(enable: false);
        _isBackgroundEnabled = false;
      }

      _isSignificantLocationEnabled = false;
      _isTracking = false;
      _emitStatus(LocationServiceStatus.stopped());

      print('Location tracking stopped (including iOS background services)');
    } catch (e) {
      print('Error stopping location tracking: $e');
      _emitStatus(LocationServiceStatus.error('Failed to stop tracking: $e'));
    }
  }

  /// Force upload all pending locations
  Future<void> forceUploadPending() async {
    await LocationUploadManager.instance.forceUpload();

    _emitStatus(LocationServiceStatus.active(
      pendingUploads: LocationUploadManager.instance.pendingCount,
      isUploading: LocationUploadManager.instance.isUploading,
    ));
  }

  /// Check if location tracking is active
  bool get isTracking => _isTracking;

  /// Check if background mode is enabled
  bool get isBackgroundEnabled => _isBackgroundEnabled;

  /// Get current service status
  LocationServiceStatus get currentStatus {
    if (!_isTracking) return LocationServiceStatus.stopped();

    return LocationServiceStatus.active(
      pendingUploads: LocationUploadManager.instance.pendingCount,
      isUploading: LocationUploadManager.instance.isUploading,
    );
  }

  void _emitStatus(LocationServiceStatus status) {
    _statusController.add(status);
  }

  /// Force location update for iOS
  Future<void> forceLocationUpdate() async {
    if (!Platform.isIOS || !_isTracking) return;

    try {
      final location = await _location.getLocation()
          .timeout(LocationConfig.iosLocationTimeout);

      if (location.latitude != null && location.longitude != null) {
        _onLocationUpdate(location);
        print('Forced iOS location update successful');
      }
    } catch (e) {
      print('Forced iOS location update failed: $e');
    }
  }

  void dispose() {
    _locationSubscription?.cancel();
    _significantLocationSubscription?.cancel();
    _iosLocationTimer?.cancel();
    _backgroundRefreshTimer?.cancel();
    _statusController.close();
    LocationUploadManager.instance.dispose();
  }
}

/// Service status data class
class LocationServiceStatus {
  final LocationServiceState state;
  final String? message;
  final LocationData? lastLocation;
  final int pendingUploads;
  final bool isUploading;
  final DateTime timestamp;

  LocationServiceStatus._({
    required this.state,
    this.message,
    this.lastLocation,
    this.pendingUploads = 0,
    this.isUploading = false,
  }) : timestamp = DateTime.now();

  factory LocationServiceStatus.active({
    LocationData? lastLocation,
    int pendingUploads = 0,
    bool isUploading = false,
  }) =>
      LocationServiceStatus._(
        state: LocationServiceState.active,
        lastLocation: lastLocation,
        pendingUploads: pendingUploads,
        isUploading: isUploading,
      );

  factory LocationServiceStatus.stopped() => LocationServiceStatus._(
        state: LocationServiceState.stopped,
      );

  factory LocationServiceStatus.error(String message) =>
      LocationServiceStatus._(
        state: LocationServiceState.error,
        message: message,
      );

  @override
  String toString() =>
      'LocationServiceStatus(state: $state, pending: $pendingUploads, uploading: $isUploading)';
}

enum LocationServiceState { active, stopped, error }

/// Result class for service operations
class LocationServiceResult {
  final bool isSuccess;
  final String message;

  LocationServiceResult._(this.isSuccess, this.message);

  factory LocationServiceResult.success(String message) =>
      LocationServiceResult._(true, message);

  factory LocationServiceResult.failure(String message) =>
      LocationServiceResult._(false, message);

  @override
  String toString() =>
      'LocationServiceResult(success: $isSuccess, message: $message)';
}

/// Colors class for notification (if needed)
class Color {
  final int value;
  const Color(this.value);
}

Future<LocationServiceResult> checkAndRequestBackgroundLocation() async {
  PermissionStatus bgStatus;

  if (Platform.isIOS) {
    bgStatus = await Permission.locationAlways.request();
  } else {
    bgStatus = await Permission.locationAlways.status;
    if (bgStatus.isDenied) {
      bgStatus = await Permission.locationAlways.request();
    }
  }

  if (bgStatus != PermissionStatus.granted) {
    return LocationServiceResult.failure(
      'Background location permission denied',
    );
  }

  return LocationServiceResult.success(
      'Background location permission granted');
}
